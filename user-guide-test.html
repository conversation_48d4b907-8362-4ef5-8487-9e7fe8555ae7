<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户引导系统测试</title>
    <link rel="stylesheet" href="user-guide.css">
    <style>
        body {
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            color: #2196f3;
            margin-bottom: 10px;
        }
        
        .test-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 150px;
        }
        
        .test-btn.primary {
            background: #2196f3;
            color: white;
        }
        
        .test-btn.primary:hover {
            background: #1976d2;
            transform: translateY(-2px);
        }
        
        .test-btn.secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e0e0e0;
        }
        
        .test-btn.secondary:hover {
            border-color: #2196f3;
            color: #2196f3;
        }
        
        .test-elements {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .test-element {
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            text-align: center;
            background: #fafafa;
            transition: all 0.2s ease;
        }
        
        .test-element:hover {
            border-color: #2196f3;
            background: white;
        }
        
        .test-element h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .test-element p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        
        .sidebar {
            position: fixed;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 200px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .sidebar h3 {
            margin: 0 0 15px 0;
            color: #2196f3;
        }
        
        .sidebar ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .sidebar li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .sidebar li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="sidebar" id="test-sidebar">
        <h3>📋 导航菜单</h3>
        <ul>
            <li>🏠 仪表盘</li>
            <li>📦 产品库</li>
            <li>👥 建联记录</li>
            <li>🤖 AI助手</li>
        </ul>
    </div>

    <div class="test-container">
        <div class="test-header">
            <h1>🎯 用户引导系统测试</h1>
            <p>测试修复后的用户引导功能和视觉样式</p>
        </div>
        
        <div class="test-buttons">
            <button class="test-btn primary" id="start-guide-btn">🚀 启动用户引导</button>
            <button class="test-btn secondary" id="test-driver-btn">🔧 测试Driver.js</button>
            <button class="test-btn secondary" id="reset-guide-btn">🔄 重置引导状态</button>
        </div>
        
        <div class="test-elements">
            <div class="test-element" id="element-1">
                <h3>📊 数据分析</h3>
                <p>查看详细的数据分析报告</p>
            </div>
            
            <div class="test-element" id="element-2">
                <h3>📝 内容管理</h3>
                <p>管理您的内容和素材</p>
            </div>
            
            <div class="test-element" id="element-3">
                <h3>⚙️ 系统设置</h3>
                <p>配置系统参数和偏好</p>
            </div>
            
            <div class="test-element" id="element-4">
                <h3>📞 联系支持</h3>
                <p>获取技术支持和帮助</p>
            </div>
        </div>
    </div>

    <!-- 加载Driver.js -->
    <script src="libs/driver.js/driver.min.js"></script>
    
    <!-- 加载用户引导系统 -->
    <script src="user-guide.js"></script>
    
    <script>
        // 等待页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 用户引导测试页面已加载');
            
            // 初始化用户引导系统
            if (typeof UserGuide !== 'undefined') {
                UserGuide.init().then(() => {
                    console.log('✅ 用户引导系统初始化完成');
                });
            }
            
            // 启动用户引导按钮
            document.getElementById('start-guide-btn').addEventListener('click', function() {
                console.log('🎯 启动用户引导测试');
                if (window.UserGuide && window.UserGuide.restart) {
                    UserGuide.restart();
                } else {
                    console.error('❌ UserGuide 不可用');
                    testDriverDirectly();
                }
            });
            
            // 测试Driver.js按钮
            document.getElementById('test-driver-btn').addEventListener('click', function() {
                console.log('🔧 直接测试Driver.js');
                testDriverDirectly();
            });
            
            // 重置引导状态按钮
            document.getElementById('reset-guide-btn').addEventListener('click', function() {
                console.log('🔄 重置引导状态');
                if (window.UserGuide && window.UserGuide.reset) {
                    UserGuide.reset();
                    alert('引导状态已重置');
                } else {
                    localStorage.removeItem('user_guide_completed');
                    localStorage.removeItem('user_guide_version');
                    alert('引导状态已重置（手动）');
                }
            });
            
            // 直接测试Driver.js功能
            function testDriverDirectly() {
                const DriverConstructor = window.driver?.driver || window.Driver;
                if (!DriverConstructor) {
                    alert('Driver.js 未加载');
                    return;
                }
                
                const driverInstance = DriverConstructor({
                    className: 'user-guide-driver-enhanced',
                    animate: true,
                    opacity: 0.75,
                    padding: 15,
                    showProgress: true,
                    doneBtnText: '完成测试',
                    closeBtnText: '跳过',
                    nextBtnText: '下一步',
                    prevBtnText: '上一步',
                    steps: [
                        {
                            element: 'body',
                            popover: {
                                title: '👋 欢迎使用测试页面！',
                                description: '这是用户引导系统的测试页面。我们将测试<strong>半透明遮罩</strong>和<strong>高对比度文字</strong>的效果。',
                                position: 'center'
                            }
                        },
                        {
                            element: '#test-sidebar',
                            popover: {
                                title: '📋 侧边栏导航',
                                description: '这是侧边栏导航区域，包含主要的功能模块。注意文字的<strong>清晰度</strong>和<strong>可读性</strong>。',
                                position: 'right'
                            }
                        },
                        {
                            element: '#start-guide-btn',
                            popover: {
                                title: '🚀 启动按钮',
                                description: '这是启动用户引导的主要按钮。测试按钮的<strong>视觉效果</strong>和<strong>交互反馈</strong>。',
                                position: 'bottom'
                            }
                        },
                        {
                            element: '.test-elements',
                            popover: {
                                title: '📊 功能区域',
                                description: '这里展示了各种功能模块。引导系统应该能够<strong>清晰突出</strong>目标元素。',
                                position: 'top'
                            }
                        },
                        {
                            element: 'body',
                            popover: {
                                title: '🎉 测试完成！',
                                description: '用户引导系统测试完成。如果您能清晰看到所有文字和界面元素，说明修复成功！',
                                position: 'center'
                            }
                        }
                    ]
                });
                
                driverInstance.drive();
            }
        });
    </script>
</body>
</html>
